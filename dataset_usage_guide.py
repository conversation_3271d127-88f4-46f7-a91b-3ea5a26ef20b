#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿精细运动发展指导数据集使用指南
展示如何使用构建的数据集进行LLM训练和应用
"""

import json
import random
from typing import Dict, List, Any

class DatasetUsageGuide:
    def __init__(self, dataset_file: str = 'basic_fine_motor_dataset.json'):
        """初始化数据集使用指南"""
        self.dataset_file = dataset_file
        self.dataset = self.load_dataset()
    
    def load_dataset(self) -> Dict:
        """加载数据集"""
        try:
            with open(self.dataset_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载数据集失败: {e}")
            return {}
    
    def get_dataset_overview(self) -> Dict:
        """获取数据集概览"""
        if not self.dataset:
            return {}
        
        metadata = self.dataset.get('metadata', {})
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        
        # 统计分析
        categories = {}
        age_groups = {}
        difficulties = {}
        
        for qa in qa_pairs:
            # 类别统计
            category = qa.get('category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
            
            # 年龄组统计
            age_group = qa.get('age_group', 'unknown')
            age_groups[age_group] = age_groups.get(age_group, 0) + 1
            
            # 难度统计
            difficulty = qa.get('difficulty', 'unknown')
            difficulties[difficulty] = difficulties.get(difficulty, 0) + 1
        
        return {
            'metadata': metadata,
            'total_samples': len(qa_pairs),
            'categories': categories,
            'age_groups': age_groups,
            'difficulties': difficulties
        }
    
    def filter_by_category(self, category: str) -> List[Dict]:
        """按类别筛选数据"""
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        return [qa for qa in qa_pairs if qa.get('category') == category]
    
    def filter_by_age_group(self, age_group: str) -> List[Dict]:
        """按年龄组筛选数据"""
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        return [qa for qa in qa_pairs if qa.get('age_group') == age_group]
    
    def filter_by_difficulty(self, difficulty: str) -> List[Dict]:
        """按难度筛选数据"""
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        return [qa for qa in qa_pairs if qa.get('difficulty') == difficulty]
    
    def get_training_samples(self, sample_size: int = 100) -> List[Dict]:
        """获取训练样本"""
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        if len(qa_pairs) <= sample_size:
            return qa_pairs
        return random.sample(qa_pairs, sample_size)
    
    def format_for_llm_training(self, samples: List[Dict]) -> List[Dict]:
        """格式化为LLM训练格式"""
        formatted_samples = []
        
        for sample in samples:
            # 标准对话格式
            formatted_sample = {
                'messages': [
                    {
                        'role': 'system',
                        'content': '你是一位专业的儿童发育专家，专门指导0-3岁婴幼儿精细运动发展。请基于专业知识提供准确、实用的建议。'
                    },
                    {
                        'role': 'user',
                        'content': sample['question']
                    },
                    {
                        'role': 'assistant',
                        'content': sample['answer']
                    }
                ],
                'metadata': {
                    'category': sample.get('category'),
                    'age_group': sample.get('age_group'),
                    'difficulty': sample.get('difficulty')
                }
            }
            formatted_samples.append(formatted_sample)
        
        return formatted_samples
    
    def generate_prompt_templates(self) -> Dict[str, str]:
        """生成提示词模板"""
        templates = {
            'milestone_inquiry': """
作为儿童发育专家，请回答关于{age_group}宝宝精细动作发育标准的问题。

问题：{question}

请提供：
1. 具体的发育标准
2. 观察要点
3. 正常范围说明
""",
            
            'evaluation_method': """
作为专业评估师，请详细说明{age_group}宝宝精细动作的测评方法。

问题：{question}

请包含：
1. 具体测评步骤
2. 评判标准
3. 注意事项
""",
            
            'training_guide': """
作为早教专家，请为{age_group}宝宝提供精细动作训练指导。

问题：{question}

请提供：
1. 训练目标
2. 具体方法
3. 安全提醒
""",
            
            'abnormal_detection': """
作为儿科医生，请说明{age_group}宝宝精细动作发育异常的识别方法。

问题：{question}

请包含：
1. 警示信号
2. 评估建议
3. 就医指导
"""
        }
        
        return templates
    
    def create_fine_tuning_dataset(self, output_file: str = 'fine_tuning_dataset.jsonl'):
        """创建微调数据集"""
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        formatted_samples = self.format_for_llm_training(qa_pairs)
        
        # 保存为JSONL格式（每行一个JSON对象）
        with open(output_file, 'w', encoding='utf-8') as f:
            for sample in formatted_samples:
                f.write(json.dumps(sample, ensure_ascii=False) + '\n')
        
        print(f"✅ 微调数据集已保存到: {output_file}")
        print(f"📊 总样本数: {len(formatted_samples)}")
        
        return output_file
    
    def create_evaluation_set(self, test_ratio: float = 0.2, output_file: str = 'evaluation_set.json'):
        """创建评估数据集"""
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        
        # 随机分割训练集和测试集
        random.shuffle(qa_pairs)
        split_index = int(len(qa_pairs) * (1 - test_ratio))
        
        train_set = qa_pairs[:split_index]
        test_set = qa_pairs[split_index:]
        
        evaluation_data = {
            'train_set': train_set,
            'test_set': test_set,
            'split_ratio': {
                'train': 1 - test_ratio,
                'test': test_ratio
            },
            'statistics': {
                'total_samples': len(qa_pairs),
                'train_samples': len(train_set),
                'test_samples': len(test_set)
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(evaluation_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 评估数据集已保存到: {output_file}")
        print(f"📊 训练集: {len(train_set)} 样本")
        print(f"📊 测试集: {len(test_set)} 样本")
        
        return evaluation_data
    
    def demonstrate_usage(self):
        """演示数据集使用方法"""
        print("🎯 婴幼儿精细运动发展指导数据集使用演示")
        print("="*50)
        
        # 1. 数据集概览
        print("\n📊 数据集概览:")
        overview = self.get_dataset_overview()
        print(f"   总样本数: {overview['total_samples']}")
        print(f"   类别数: {len(overview['categories'])}")
        print(f"   年龄组数: {len(overview['age_groups'])}")
        
        # 2. 类别分布
        print("\n📈 类别分布:")
        for category, count in sorted(overview['categories'].items()):
            percentage = (count / overview['total_samples']) * 100
            print(f"   {category}: {count} ({percentage:.1f}%)")
        
        # 3. 筛选示例
        print("\n🔍 数据筛选示例:")
        milestone_qa = self.filter_by_category('milestone_inquiry')
        print(f"   里程碑询问类问答: {len(milestone_qa)} 个")
        
        if milestone_qa:
            sample = milestone_qa[0]
            print(f"   示例问题: {sample['question']}")
            print(f"   示例答案: {sample['answer'][:100]}...")
        
        # 4. 年龄组筛选
        print("\n👶 年龄组筛选示例:")
        six_month_qa = self.filter_by_age_group('6个月')
        print(f"   6个月宝宝相关问答: {len(six_month_qa)} 个")
        
        # 5. 难度筛选
        print("\n📚 难度分布:")
        for difficulty, count in sorted(overview['difficulties'].items()):
            percentage = (count / overview['total_samples']) * 100
            print(f"   {difficulty}: {count} ({percentage:.1f}%)")
        
        # 6. 训练样本格式化
        print("\n🤖 LLM训练格式示例:")
        sample_data = self.get_training_samples(3)
        formatted_samples = self.format_for_llm_training(sample_data)
        
        if formatted_samples:
            sample = formatted_samples[0]
            print("   格式化样本:")
            print(f"   系统提示: {sample['messages'][0]['content'][:80]}...")
            print(f"   用户问题: {sample['messages'][1]['content']}")
            print(f"   助手回答: {sample['messages'][2]['content'][:100]}...")
    
    def export_for_specific_use_case(self, use_case: str, output_file: str = None):
        """为特定用例导出数据"""
        qa_pairs = self.dataset.get('data', {}).get('qa_pairs', [])
        
        if use_case == 'parent_education':
            # 家长教育：基础和中等难度
            filtered_data = [qa for qa in qa_pairs if qa.get('difficulty') in ['basic', 'intermediate']]
            output_file = output_file or 'parent_education_dataset.json'
            
        elif use_case == 'professional_training':
            # 专业培训：中等和高级难度
            filtered_data = [qa for qa in qa_pairs if qa.get('difficulty') in ['intermediate', 'advanced']]
            output_file = output_file or 'professional_training_dataset.json'
            
        elif use_case == 'early_intervention':
            # 早期干预：异常检测和评估方法
            filtered_data = [qa for qa in qa_pairs if qa.get('category') in ['abnormal_detection', 'evaluation_method']]
            output_file = output_file or 'early_intervention_dataset.json'
            
        elif use_case == 'milestone_tracking':
            # 里程碑追踪：里程碑询问和发育评估
            filtered_data = [qa for qa in qa_pairs if qa.get('category') in ['milestone_inquiry', 'development_assessment']]
            output_file = output_file or 'milestone_tracking_dataset.json'
            
        else:
            print(f"❌ 不支持的用例: {use_case}")
            return
        
        export_data = {
            'use_case': use_case,
            'description': f'专门用于{use_case}的精细运动发育指导数据集',
            'total_samples': len(filtered_data),
            'data': filtered_data,
            'export_time': json.dumps(None, default=str)  # 当前时间
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ {use_case}数据集已导出到: {output_file}")
        print(f"📊 包含 {len(filtered_data)} 个样本")

def main():
    """主函数"""
    print("📚 婴幼儿精细运动发展指导数据集使用指南")
    print("="*50)
    
    # 创建使用指南实例
    guide = DatasetUsageGuide()
    
    # 演示基本使用方法
    guide.demonstrate_usage()
    
    print("\n🛠️ 实用工具演示:")
    
    # 创建微调数据集
    print("\n1. 创建LLM微调数据集:")
    guide.create_fine_tuning_dataset()
    
    # 创建评估数据集
    print("\n2. 创建模型评估数据集:")
    guide.create_evaluation_set()
    
    # 导出特定用例数据
    print("\n3. 导出特定用例数据集:")
    use_cases = ['parent_education', 'professional_training', 'early_intervention', 'milestone_tracking']
    
    for use_case in use_cases:
        guide.export_for_specific_use_case(use_case)
    
    print("\n🎉 数据集使用指南演示完成！")
    print("\n📁 生成的文件:")
    print("   - fine_tuning_dataset.jsonl (LLM微调格式)")
    print("   - evaluation_set.json (训练/测试分割)")
    print("   - parent_education_dataset.json (家长教育专用)")
    print("   - professional_training_dataset.json (专业培训专用)")
    print("   - early_intervention_dataset.json (早期干预专用)")
    print("   - milestone_tracking_dataset.json (里程碑追踪专用)")

if __name__ == "__main__":
    main()
