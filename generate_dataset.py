import json
import os
import dashscope
from dashscope import Generation
import getpass

# Set your Qwen API key here or via environment variable
dashscope.api_key = os.getenv('QWEN_API_KEY') or getpass.getpass("Enter Qwen API key: ")  # Replace with your actual key or set env var

# List of JSON files to process
json_files = [
    'baby_fine_motor_development_cleaned1.json',
    '《人体发育学学习指导及习题集》 (Pdg2Pic, 陈翔主编；吕智海，李林，李晓捷等编) (Z-Library)-已压缩/tmp-convert-17532588008816385.json',
    '人体发育学 第2版 (李晓捷主编, 主编李晓捷, 李晓捷, 李晓捷主编, 李晓捷) (Z-Library)/tmp-convert-17532586738269494.json',
    '人体发育学 第2版 (江钟立主编, 江钟立主编, 江钟立) (Z-Library)/tmp-convert-17532585662476209.json',
    '人体发育学粗大运动 (左天香，徐冬晨主编, 左天香, 徐冬晨主编, 左天香, 徐冬晨) (Z-Library)/tmp-convert-17532585448983525.json'
]

# Function to load content from JSON
def load_content(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    # Extract relevant content (adjust based on JSON structure)
    if 'content' in data:
        return data['content']
    elif 'pages' in data:  # For book JSONs with nested content
        return ' '.join([item['text'] for page in data['pages'] for item in page.get('content', []) if 'text' in item])
    return ''

# Collect all contents
all_contents = []
for file in json_files:
    content = load_content(file)
    if content:
        all_contents.append(content)

combined_content = '\n'.join(all_contents)

# Function to generate data using Qwen
def generate_with_qwen(prompt):
    messages = [{'role': 'system', 'content': 'You are a helpful assistant for generating infant fine motor development datasets.'},
                {'role': 'user', 'content': prompt}]
    response = Generation.call(
        model='qwen-max',
        messages=messages,
        result_format='message'
    )
    if response.status_code == 200:
        return response.output.choices[0].message.content
    else:
        raise Exception(f"API error: {response.message}")

# Generate dataset
dataset = []
age_groups = ['0-3 months', '4-6 months', '7-9 months', '10-12 months', '13-18 months', '19-24 months', '25-36 months']

for age in age_groups:
    prompt = f"Based on the following content about infant fine motor development, generate 5 sample guidance texts for {age} babies, including milestones, evaluation methods, and training tips:\n\n{combined_content[:2000]}"  # Truncate if too long
    generated = generate_with_qwen(prompt)
    dataset.append({'age_group': age, 'guidance': generated})

# Save to file
with open('fine_motor_dataset.json', 'w', encoding='utf-8') as f:
    json.dump(dataset, f, ensure_ascii=False, indent=4)

print("Dataset generated and saved to fine_motor_dataset.json") 