#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心图片下载脚本
专门用于下载文章中的核心图片（wximg目录下的图片）
"""

import requests
import json
import time
from pathlib import Path
import os

def download_core_images_from_json(json_file, output_dir="core_images"):
    """从JSON文件中读取图片信息并下载核心图片"""
    
    # 读取JSON文件
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到文件: {json_file}")
        return
    except json.JSONDecodeError:
        print(f"❌ JSON文件格式错误: {json_file}")
        return
    
    images = data.get('images', [])
    if not images:
        print("❌ 没有找到图片信息")
        return
    
    # 创建输出目录
    img_dir = Path(output_dir)
    img_dir.mkdir(exist_ok=True)
    
    # 创建session
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    })
    
    downloaded_images = []
    core_image_count = 0
    
    print(f"🔍 开始筛选和下载核心图片...")
    print(f"📁 保存目录: {output_dir}")
    print("-" * 50)
    
    for img_info in images:
        img_url = img_info['src']
        
        # 只下载文章核心图片（wximg目录下的图片）
        if 'wximg' in img_url or ('upload/20210505' in img_url and 'wximg' not in img_url):
            try:
                print(f"📥 正在下载核心图片 {core_image_count + 1}: {img_url}")
                
                # 获取图片
                response = session.get(img_url, timeout=30)
                response.raise_for_status()
                
                # 生成文件名
                img_name = img_url.split('/')[-1]
                if not img_name or '.' not in img_name:
                    # 如果没有扩展名，尝试从Content-Type获取
                    content_type = response.headers.get('content-type', '')
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        img_name = f"baby_action_{core_image_count + 1:02d}.jpg"
                    elif 'png' in content_type:
                        img_name = f"baby_action_{core_image_count + 1:02d}.png"
                    elif 'gif' in content_type:
                        img_name = f"baby_action_{core_image_count + 1:02d}.gif"
                    else:
                        img_name = f"baby_action_{core_image_count + 1:02d}.jpg"
                else:
                    # 重命名为更有意义的文件名
                    ext = img_name.split('.')[-1] if '.' in img_name else 'jpg'
                    img_name = f"baby_action_{core_image_count + 1:02d}.{ext}"
                
                # 保存图片
                img_path = img_dir / img_name
                with open(img_path, 'wb') as f:
                    f.write(response.content)
                
                # 获取图片大小
                img_size = len(response.content)
                img_size_kb = img_size / 1024
                
                downloaded_images.append({
                    'original_url': img_url,
                    'local_path': str(img_path),
                    'filename': img_name,
                    'size_bytes': img_size,
                    'size_kb': round(img_size_kb, 2),
                    'alt': img_info.get('alt', ''),
                    'title': img_info.get('title', '')
                })
                
                core_image_count += 1
                print(f"✅ 下载成功: {img_name} ({img_size_kb:.1f} KB)")
                
                # 添加延时，避免请求过于频繁
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ 下载失败 {img_url}: {e}")
                continue
    
    # 保存下载记录
    download_record = {
        'download_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'total_downloaded': core_image_count,
        'output_directory': output_dir,
        'images': downloaded_images
    }
    
    record_file = img_dir / 'download_record.json'
    with open(record_file, 'w', encoding='utf-8') as f:
        json.dump(download_record, f, ensure_ascii=False, indent=2)
    
    print("-" * 50)
    print(f"🎯 核心图片下载完成！")
    print(f"📊 统计信息:")
    print(f"   - 总共下载: {core_image_count} 张核心图片")
    print(f"   - 保存目录: {output_dir}")
    print(f"   - 下载记录: {record_file}")
    
    # 显示图片列表
    if downloaded_images:
        print(f"\n📋 下载的图片列表:")
        total_size = 0
        for i, img in enumerate(downloaded_images, 1):
            print(f"   {i:2d}. {img['filename']} ({img['size_kb']} KB)")
            total_size += img['size_bytes']
        
        print(f"\n💾 总大小: {total_size / 1024:.1f} KB ({total_size / (1024*1024):.2f} MB)")
    
    return downloaded_images

def main():
    """主函数"""
    json_file = "baby_fine_motor_development.json"
    output_dir = "baby_core_images"
    
    print("🖼️  宝宝精细动作核心图片下载器")
    print("=" * 50)
    
    if not os.path.exists(json_file):
        print(f"❌ 找不到JSON文件: {json_file}")
        print("请先运行 web_scraper.py 生成JSON文件")
        return
    
    # 下载核心图片
    downloaded_images = download_core_images_from_json(json_file, output_dir)
    
    if downloaded_images:
        print(f"\n🎉 所有核心图片已下载到 '{output_dir}' 目录中！")
        print("这些图片展示了0-3岁宝宝各个月龄的精细动作发育标准。")
    else:
        print("\n😔 没有下载到任何核心图片")

if __name__ == "__main__":
    main()
