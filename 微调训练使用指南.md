# 婴幼儿精细运动发展指导数据集 - 微调训练使用指南

## 📋 数据集概览

### 🎯 转换完成的数据集
- **JSON格式**: `datasets/behavior_analysis_instruction_format.json` (3.6MB)
  - 包含完整元数据，适合研究分析
  - 总样本数：300条
  - 包含年龄组、类别、难度等详细信息

- **JSONL格式**: `datasets/behavior_analysis_instruction_format.jsonl` (1.2MB)
  - 精简版本，适合直接用于训练
  - 每行一个JSON对象，便于流式处理
  - 只包含instruction、input、output三个核心字段

### 📊 数据分布特征
- **年龄组分布**: general(32.3%), 6个月(17.3%), 18个月(16.3%), 30个月(12.0%)等
- **类别**: 100%为behavior_analysis（行为分析）
- **难度**: 100%为intermediate（中等难度）

---

## 🔧 微调训练实现

### 1. 使用Transformers库进行微调

#### 安装依赖
```bash
pip install transformers datasets torch accelerate
```

#### 基础微调脚本
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Transformers库微调大模型
"""

import json
import torch
from datasets import Dataset
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)

class FineTuningTrainer:
    def __init__(self, model_name: str = "Qwen/Qwen2-7B-Instruct"):
        self.model_name = model_name
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_dataset(self, jsonl_file: str = "datasets/behavior_analysis_instruction_format.jsonl"):
        """加载JSONL格式数据集"""
        data = []
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line in f:
                data.append(json.loads(line))
        
        return Dataset.from_list(data)
    
    def format_prompt(self, sample):
        """格式化训练样本"""
        instruction = sample['instruction']
        input_text = sample['input']
        output_text = sample['output']
        
        # 构建完整的训练文本
        full_text = f"<|im_start|>system\n{instruction}<|im_end|>\n<|im_start|>user\n{input_text}<|im_end|>\n<|im_start|>assistant\n{output_text}<|im_end|>"
        
        return {"text": full_text}
    
    def tokenize_function(self, examples):
        """分词函数"""
        return self.tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=2048,
            return_overflowing_tokens=False,
        )
    
    def train(self, output_dir: str = "./fine_tuned_model"):
        """开始微调训练"""
        # 加载和预处理数据
        dataset = self.load_dataset()
        dataset = dataset.map(self.format_prompt)
        dataset = dataset.map(self.tokenize_function, batched=True)
        
        # 分割训练集和验证集
        train_dataset = dataset.select(range(int(len(dataset) * 0.9)))
        eval_dataset = dataset.select(range(int(len(dataset) * 0.9), len(dataset)))
        
        # 训练参数
        training_args = TrainingArguments(
            output_dir=output_dir,
            overwrite_output_dir=True,
            num_train_epochs=3,
            per_device_train_batch_size=4,
            per_device_eval_batch_size=4,
            gradient_accumulation_steps=2,
            warmup_steps=100,
            learning_rate=2e-5,
            logging_steps=10,
            save_steps=500,
            eval_steps=500,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to=None,  # 禁用wandb等日志
            dataloader_pin_memory=False,
        )
        
        # 数据整理器
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            data_collator=data_collator,
        )
        
        # 开始训练
        print("🚀 开始微调训练...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.tokenizer.save_pretrained(output_dir)
        print(f"✅ 模型已保存到: {output_dir}")

# 使用示例
if __name__ == "__main__":
    trainer = FineTuningTrainer()
    trainer.train()
```

### 2. 使用LoRA进行高效微调

#### 安装PEFT库
```bash
pip install peft
```

#### LoRA微调脚本
```python
from peft import LoraConfig, get_peft_model, TaskType

class LoRAFineTuner(FineTuningTrainer):
    def __init__(self, model_name: str = "Qwen/Qwen2-7B-Instruct"):
        super().__init__(model_name)
        
        # LoRA配置
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,  # LoRA rank
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"]
        )
        
        # 应用LoRA
        self.model = get_peft_model(self.model, lora_config)
        self.model.print_trainable_parameters()
    
    def train(self, output_dir: str = "./lora_fine_tuned_model"):
        """LoRA微调训练"""
        # 使用更小的学习率和批次大小
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=5,
            per_device_train_batch_size=2,
            gradient_accumulation_steps=4,
            learning_rate=1e-4,  # LoRA通常使用更高的学习率
            warmup_steps=50,
            logging_steps=10,
            save_steps=200,
            eval_steps=200,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
        )
        
        # 继续使用父类的训练逻辑
        super().train_with_args(training_args)
```

### 3. 使用DeepSpeed进行大规模训练

#### DeepSpeed配置文件 (ds_config.json)
```json
{
    "train_batch_size": 16,
    "train_micro_batch_size_per_gpu": 2,
    "gradient_accumulation_steps": 8,
    "optimizer": {
        "type": "AdamW",
        "params": {
            "lr": 2e-5,
            "betas": [0.9, 0.999],
            "eps": 1e-8,
            "weight_decay": 0.01
        }
    },
    "scheduler": {
        "type": "WarmupLR",
        "params": {
            "warmup_min_lr": 0,
            "warmup_max_lr": 2e-5,
            "warmup_num_steps": 100
        }
    },
    "zero_optimization": {
        "stage": 2,
        "allgather_partitions": true,
        "allgather_bucket_size": 2e8,
        "overlap_comm": true,
        "reduce_scatter": true,
        "reduce_bucket_size": 2e8,
        "contiguous_gradients": true
    },
    "fp16": {
        "enabled": true,
        "loss_scale": 0,
        "loss_scale_window": 1000,
        "hysteresis": 2,
        "min_loss_scale": 1
    },
    "wall_clock_breakdown": false
}
```

---

## 🎯 不同框架的使用方法

### 1. OpenAI格式微调

#### 转换为OpenAI格式
```python
def convert_to_openai_format():
    """转换为OpenAI微调格式"""
    openai_data = []
    
    with open('datasets/behavior_analysis_instruction_format.jsonl', 'r', encoding='utf-8') as f:
        for line in f:
            sample = json.loads(line)
            
            openai_sample = {
                "messages": [
                    {"role": "system", "content": sample["instruction"]},
                    {"role": "user", "content": sample["input"]},
                    {"role": "assistant", "content": sample["output"]}
                ]
            }
            openai_data.append(openai_sample)
    
    # 保存为OpenAI格式
    with open('datasets/openai_format.jsonl', 'w', encoding='utf-8') as f:
        for sample in openai_data:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')
```

### 2. Alpaca格式微调

#### 转换为Alpaca格式
```python
def convert_to_alpaca_format():
    """转换为Alpaca格式"""
    alpaca_data = []
    
    with open('datasets/behavior_analysis_instruction_format.jsonl', 'r', encoding='utf-8') as f:
        for line in f:
            sample = json.loads(line)
            
            alpaca_sample = {
                "instruction": sample["instruction"],
                "input": sample["input"],
                "output": sample["output"]
            }
            alpaca_data.append(alpaca_sample)
    
    # 保存为Alpaca格式
    with open('datasets/alpaca_format.json', 'w', encoding='utf-8') as f:
        json.dump(alpaca_data, f, ensure_ascii=False, indent=2)
```

### 3. ChatGLM格式微调

#### 转换为ChatGLM格式
```python
def convert_to_chatglm_format():
    """转换为ChatGLM格式"""
    chatglm_data = []
    
    with open('datasets/behavior_analysis_instruction_format.jsonl', 'r', encoding='utf-8') as f:
        for line in f:
            sample = json.loads(line)
            
            # ChatGLM使用特殊的对话格式
            conversation = [
                {"role": "user", "content": f"{sample['instruction']}\n\n{sample['input']}"},
                {"role": "assistant", "content": sample["output"]}
            ]
            
            chatglm_data.append({"conversations": conversation})
    
    # 保存为ChatGLM格式
    with open('datasets/chatglm_format.jsonl', 'w', encoding='utf-8') as f:
        for sample in chatglm_data:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')
```

---

## ⚙️ 训练参数建议

### 🎯 基础参数设置

#### 小模型 (7B以下)
```python
training_args = TrainingArguments(
    num_train_epochs=3,
    per_device_train_batch_size=8,
    gradient_accumulation_steps=2,
    learning_rate=2e-5,
    warmup_steps=100,
    max_grad_norm=1.0,
    weight_decay=0.01,
)
```

#### 中等模型 (7B-13B)
```python
training_args = TrainingArguments(
    num_train_epochs=3,
    per_device_train_batch_size=4,
    gradient_accumulation_steps=4,
    learning_rate=1e-5,
    warmup_steps=200,
    max_grad_norm=1.0,
    weight_decay=0.01,
)
```

#### 大模型 (13B以上)
```python
training_args = TrainingArguments(
    num_train_epochs=2,
    per_device_train_batch_size=2,
    gradient_accumulation_steps=8,
    learning_rate=5e-6,
    warmup_steps=300,
    max_grad_norm=1.0,
    weight_decay=0.01,
)
```

### 🔧 LoRA参数建议

```python
lora_config = LoraConfig(
    r=16,  # rank，控制参数量
    lora_alpha=32,  # 缩放因子
    lora_dropout=0.1,
    target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 目标模块
    bias="none",
    task_type=TaskType.CAUSAL_LM,
)
```

---

## 📊 评估与验证

### 1. 自动评估脚本

```python
def evaluate_model(model_path: str, test_data_path: str):
    """评估微调后的模型"""
    from transformers import pipeline
    import json
    from rouge import Rouge
    from nltk.translate.bleu_score import sentence_bleu
    
    # 加载模型
    generator = pipeline(
        "text-generation",
        model=model_path,
        tokenizer=model_path,
        device=0 if torch.cuda.is_available() else -1
    )
    
    # 加载测试数据
    test_samples = []
    with open(test_data_path, 'r', encoding='utf-8') as f:
        for line in f:
            test_samples.append(json.loads(line))
    
    # 评估指标
    rouge = Rouge()
    bleu_scores = []
    rouge_scores = []
    
    for sample in test_samples[:50]:  # 评估前50个样本
        # 生成回答
        prompt = f"{sample['instruction']}\n\n{sample['input']}"
        generated = generator(prompt, max_length=1024, num_return_sequences=1)[0]['generated_text']
        
        # 提取生成的回答部分
        generated_answer = generated[len(prompt):].strip()
        reference_answer = sample['output']
        
        # 计算BLEU分数
        bleu = sentence_bleu([reference_answer.split()], generated_answer.split())
        bleu_scores.append(bleu)
        
        # 计算ROUGE分数
        try:
            rouge_score = rouge.get_scores(generated_answer, reference_answer)[0]
            rouge_scores.append(rouge_score)
        except:
            continue
    
    # 输出评估结果
    avg_bleu = sum(bleu_scores) / len(bleu_scores)
    avg_rouge_1 = sum([score['rouge-1']['f'] for score in rouge_scores]) / len(rouge_scores)
    avg_rouge_l = sum([score['rouge-l']['f'] for score in rouge_scores]) / len(rouge_scores)
    
    print(f"平均BLEU分数: {avg_bleu:.4f}")
    print(f"平均ROUGE-1分数: {avg_rouge_1:.4f}")
    print(f"平均ROUGE-L分数: {avg_rouge_l:.4f}")
```

### 2. 人工评估标准

#### 专业准确性评估
- **发育理论正确性**: 是否符合儿童发育学理论
- **年龄适宜性**: 建议是否适合对应年龄组
- **安全性**: 指导建议是否安全可行

#### 实用性评估
- **可操作性**: 建议是否具体可执行
- **语言通俗性**: 是否易于家长理解
- **完整性**: 是否包含四个必需部分

---

## 🚀 部署与应用

### 1. 模型推理服务

```python
from fastapi import FastAPI
from transformers import pipeline
import uvicorn

app = FastAPI()

# 加载微调后的模型
generator = pipeline(
    "text-generation",
    model="./fine_tuned_model",
    tokenizer="./fine_tuned_model",
    device=0
)

@app.post("/analyze_behavior")
async def analyze_behavior(request: dict):
    """行为分析API接口"""
    age_group = request.get("age_group", "general")
    observation = request.get("observation", "")
    
    # 构建指令
    if age_group != "general":
        instruction = f"你是一位专业的儿童发育专家，请根据家长描述的婴幼儿行为观察，提供专业的分析和指导建议。请特别关注{age_group}宝宝的发育特点。"
    else:
        instruction = "你是一位专业的儿童发育专家，请根据家长描述的婴幼儿行为观察，提供专业的分析和指导建议。请根据宝宝的具体情况进行分析。"
    
    instruction += "\n\n请按照以下结构提供回答：\n1. **行为分析**：解释该行为的发育意义和背景\n2. **发育评估**：判断该行为是否符合相应月龄的正常发育水平\n3. **指导建议**：提供3个具体可操作的训练方法或改善建议\n4. **观察要点**：指导家长后续观察的重点和专业求助的时机"
    
    # 生成回答
    prompt = f"{instruction}\n\n{observation}"
    response = generator(prompt, max_length=1024, num_return_sequences=1)[0]['generated_text']
    
    # 提取生成的回答
    answer = response[len(prompt):].strip()
    
    return {"analysis": answer}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 2. 批量推理脚本

```python
def batch_inference(model_path: str, input_file: str, output_file: str):
    """批量推理脚本"""
    generator = pipeline("text-generation", model=model_path, tokenizer=model_path)
    
    results = []
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            sample = json.loads(line)
            prompt = f"{sample['instruction']}\n\n{sample['input']}"
            
            generated = generator(prompt, max_length=1024)[0]['generated_text']
            answer = generated[len(prompt):].strip()
            
            results.append({
                "input": sample['input'],
                "generated_output": answer,
                "reference_output": sample['output']
            })
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
```

---

## 📝 使用建议总结

### ✅ 最佳实践
1. **数据格式选择**: 
   - 研究分析使用JSON格式
   - 直接训练使用JSONL格式

2. **训练策略**:
   - 小数据集建议使用LoRA微调
   - 大GPU内存可以考虑全参数微调
   - 多GPU环境推荐DeepSpeed

3. **参数调优**:
   - 从较小的学习率开始(1e-5)
   - 监控验证集损失，避免过拟合
   - 根据GPU内存调整批次大小

4. **质量保证**:
   - 定期进行人工评估
   - 使用多种自动评估指标
   - 在真实场景中测试模型效果

### 🎯 应用场景
- **智能育儿助手**: 为家长提供专业的发育指导
- **医疗辅助系统**: 协助医生进行初步评估
- **教育培训平台**: 培训早教从业人员
- **研究工具**: 支持儿童发育相关研究

现在你已经拥有了完整的指令格式数据集和详细的使用指南，可以开始进行大模型的微调训练了！
