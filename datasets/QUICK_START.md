# 快速开始指南 (Quick Start Guide)

## 🚀 5分钟上手婴幼儿精细运动发展指导数据集

### 1️⃣ 推荐使用的数据集

根据数据质量验证结果，推荐优先使用以下数据集：

#### 🌟 最高质量数据集
- **`behavior_analysis_300_reliable.json`** - 300条行为分析+指导数据（⭐⭐⭐⭐⭐）
- **`fine_motor_200_samples.json`** - 200条里程碑查询数据（⭐⭐⭐⭐）
- **`combined_fine_motor_dataset.json`** - 827条合并数据集（⭐⭐⭐⭐）

#### 🔄 训练用数据分割
- **`train_split.json`** - 578条训练数据
- **`validation_split.json`** - 124条验证数据
- **`test_split.json`** - 125条测试数据

### 2️⃣ 基本使用方法

#### Python加载示例
```python
import json

# 加载核心数据集
with open('behavior_analysis_300_reliable.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 查看基本信息
print(f"数据集: {data['metadata']['name']}")
print(f"样本数: {len(data['data'])}")

# 查看第一个样本
sample = data['data'][0]
print(f"问题: {sample['question']}")
print(f"答案: {sample['answer'][:100]}...")
```

#### 数据结构说明
```json
{
  "metadata": {
    "name": "数据集名称",
    "version": "版本号",
    "description": "数据集描述",
    "actual_samples": 300
  },
  "data": [
    {
      "question": "家长的具体问题描述",
      "answer": "专业的结构化回答",
      "category": "数据类别",
      "age_group": "年龄组",
      "difficulty": "难度等级"
    }
  ]
}
```

### 3️⃣ 运行示例脚本

```bash
# 运行数据集使用示例
python3 dataset_usage_examples.py

# 验证数据集质量
python3 validate_datasets.py
```

### 4️⃣ 常用操作示例

#### 按年龄组筛选数据
```python
def get_samples_by_age(data, age_group):
    return [sample for sample in data['data'] 
            if sample.get('age_group') == age_group]

# 获取6个月宝宝的相关数据
six_month_samples = get_samples_by_age(data, "6个月")
print(f"6个月相关样本: {len(six_month_samples)}条")
```

#### 按类别筛选数据
```python
def get_samples_by_category(data, category):
    return [sample for sample in data['data'] 
            if sample.get('category') == category]

# 获取行为分析类数据
behavior_samples = get_samples_by_category(data, "behavior_analysis")
print(f"行为分析样本: {len(behavior_samples)}条")
```

#### 关键词搜索
```python
def search_samples(data, keyword):
    results = []
    for sample in data['data']:
        if keyword in sample['question'] or keyword in sample['answer']:
            results.append(sample)
    return results

# 搜索包含"抓握"的样本
grip_samples = search_samples(data, "抓握")
print(f"包含'抓握'的样本: {len(grip_samples)}条")
```

### 5️⃣ 数据集特色功能

#### 🌟 behavior_analysis_300_reliable.json 特色
每个答案包含四个结构化部分：
- **行为分析**：解释行为的发育意义
- **发育评估**：判断是否符合正常发育水平
- **指导建议**：提供具体可操作的训练方法
- **观察要点**：指导后续观察和专业求助时机

#### 解析结构化答案
```python
def parse_structured_answer(answer):
    sections = {}
    current_section = None
    
    for line in answer.split('**'):
        if line.strip() in ['行为分析', '发育评估', '指导建议', '观察要点']:
            current_section = line.strip()
            sections[current_section] = ""
        elif current_section and line.strip():
            sections[current_section] += line.strip()
    
    return sections

# 解析结构化答案
sample = data['data'][0]
parsed = parse_structured_answer(sample['answer'])
for section, content in parsed.items():
    print(f"{section}: {content[:50]}...")
```

### 6️⃣ 机器学习应用

#### 准备训练数据
```python
# 加载训练数据
with open('train_split.json', 'r', encoding='utf-8') as f:
    train_data = json.load(f)

# 提取问答对
questions = [sample['question'] for sample in train_data['data']]
answers = [sample['answer'] for sample in train_data['data']]

print(f"训练样本数: {len(questions)}")
```

#### 数据预处理
```python
# 简单的数据清理
def clean_text(text):
    # 移除多余空格
    text = ' '.join(text.split())
    # 移除特殊字符（可选）
    return text.strip()

cleaned_questions = [clean_text(q) for q in questions]
cleaned_answers = [clean_text(a) for a in answers]
```

### 7️⃣ 数据质量检查

#### 检查数据完整性
```python
def check_data_quality(data):
    issues = []
    
    for i, sample in enumerate(data['data']):
        # 检查必需字段
        if not sample.get('question'):
            issues.append(f"样本{i}: 缺少问题")
        if not sample.get('answer'):
            issues.append(f"样本{i}: 缺少答案")
        
        # 检查内容长度
        if len(sample.get('question', '')) < 10:
            issues.append(f"样本{i}: 问题过短")
        if len(sample.get('answer', '')) < 50:
            issues.append(f"样本{i}: 答案过短")
    
    return issues

# 检查数据质量
issues = check_data_quality(data)
if issues:
    print(f"发现{len(issues)}个问题:")
    for issue in issues[:5]:  # 只显示前5个
        print(f"  - {issue}")
else:
    print("✅ 数据质量检查通过")
```

### 8️⃣ 常见问题

#### Q: 如何选择合适的数据集？
A: 
- **AI训练**: 使用 `behavior_analysis_300_reliable.json`
- **里程碑查询**: 使用 `fine_motor_200_samples.json`
- **综合应用**: 使用 `combined_fine_motor_dataset.json`

#### Q: 数据集的编码格式是什么？
A: 所有JSON文件都使用UTF-8编码，支持中文内容。

#### Q: 如何处理结构化答案？
A: 行为分析数据集的答案包含四个部分，用`**部分名**`标记，可以用正则表达式或字符串分割解析。

#### Q: 数据集可以商用吗？
A: 当前仅供学术研究和教育用途，商业使用需要获得授权。

### 9️⃣ 进阶使用

#### 合并多个数据集
```python
def merge_datasets(file_list):
    merged_data = []
    
    for filename in file_list:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if 'data' in data:
                for sample in data['data']:
                    sample['source'] = filename
                    merged_data.append(sample)
    
    return merged_data

# 合并核心数据集
core_datasets = [
    'behavior_analysis_300_reliable.json',
    'fine_motor_200_samples.json'
]
merged = merge_datasets(core_datasets)
print(f"合并后样本数: {len(merged)}")
```

#### 创建自定义数据分割
```python
import random

def create_custom_split(data, train_ratio=0.7, val_ratio=0.15):
    samples = data['data'].copy()
    random.shuffle(samples)
    
    total = len(samples)
    train_size = int(total * train_ratio)
    val_size = int(total * val_ratio)
    
    return {
        'train': samples[:train_size],
        'validation': samples[train_size:train_size + val_size],
        'test': samples[train_size + val_size:]
    }

# 创建自定义分割
splits = create_custom_split(data)
for split_name, split_data in splits.items():
    print(f"{split_name}: {len(split_data)}条样本")
```

### 🎉 开始使用

现在你已经掌握了数据集的基本使用方法！建议：

1. 先运行 `dataset_usage_examples.py` 熟悉数据结构
2. 根据需求选择合适的数据集
3. 使用提供的代码示例进行数据处理
4. 查看 `README.md` 了解更多详细信息

**祝你使用愉快！** 🚀
