#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容清理脚本
用于清理JSON文件中与0-3岁宝宝精细动作发育标准无关的内容
"""

import json
import re
from pathlib import Path

def clean_baby_motor_content(json_file, output_file=None):
    """清理宝宝精细动作发育内容，去除无关信息"""
    
    # 读取原始JSON文件
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到文件: {json_file}")
        return None
    except json.JSONDecodeError:
        print(f"❌ JSON文件格式错误: {json_file}")
        return None
    
    print("🧹 开始清理内容...")
    
    # 提取原始内容
    original_content = data.get('content', '')
    
    # 定义需要保留的关键内容模式
    important_patterns = [
        r'"精细动作"指的是宝宝平日里.*?所产生的动作',
        r'精细动作对宝宝智力发育.*?大脑的发育',
        r'精细动作也是大脑神经发育的一个重要指标',
        r'0-[23]岁宝宝.*?精细动作.*?测评方式',
        r'▶\s*\d+个月宝宝.*?即为通过',
        r'▶\s*\d+个月宝宝.*?即可通过',
        r'测评方法：.*?即为通过',
        r'测评方法：.*?即可通过',
        r'什么是精细动作？',
        r'精细动作指的是凭借手及手指.*?精细动作',
        r'精细动作到底有多重要？',
        r'精细动作很重要.*?心灵手巧',
        r'科学家告诉我们.*?更聪明',
        r'另一方面.*?更加快乐',
        r'3岁前是宝宝精细动作能力发展.*?时期',
        r'精细动作训练参考表',
        r'下面为大家列出.*?家长参照',
        r'\d+-\d+个月精细动作发展标准：.*?建议：.*?(?=\d+-\d+个月|$)',
        r'温馨提示：.*?放嘴巴里',
        r'日本著名儿科医生.*?聪明自信的宝宝',
        r'有心理学家说.*?智力的砖瓦',
        r'今天我们来谈谈.*?多大',
        r'推荐游戏：.*?试剂瓶等'
    ]
    
    # 提取重要内容
    important_content = []
    
    for pattern in important_patterns:
        matches = re.findall(pattern, original_content, re.DOTALL | re.IGNORECASE)
        for match in matches:
            # 清理匹配到的内容
            cleaned_match = re.sub(r'\n+', '\n', match.strip())
            cleaned_match = re.sub(r'\s+', ' ', cleaned_match)
            if cleaned_match and len(cleaned_match) > 20:  # 过滤太短的内容
                important_content.append(cleaned_match)
    
    # 手动添加核心内容（从原文中提取的关键信息）
    core_content = [
        '"精细动作"指的是宝宝平日里运用自己的小手和手指的小肌肉所产生的动作，主要以拿、捏、握、屈、转为主。',
        
        '精细动作对宝宝智力发育有很大帮助，通过宝宝手脚自由活动，促进其全身运动，从而促进大脑的发育。因此，精细动作也是大脑神经发育的一个重要指标。',
        
        '▶ 1个月宝宝的精细动作：握笔杆一会儿就会掉\n测评方法：让宝宝仰卧在床上，妈妈将笔杆放在宝宝手中，宝宝可以握住2-3秒，即为通过。',
        
        '▶ 2个月宝宝的精细动作：把小手放进嘴巴里面\n测评方法：让宝宝仰卧在床上，妈妈让宝宝的手臂处于自由活动状态，如果宝宝可以自己把手放进嘴巴里面，即为通过。',
        
        '▶ 3个月宝宝的精细动作：手握手和握住玩具\n测评方法：让宝宝仰卧在床上，并且给宝宝穿比较宽松的衣服，以便手臂能够自由活动，然后妈妈观察宝宝两只手的动作，如果宝宝两只手能够放在胸前互相握住的话，即为通过；然后，将拨浪鼓或者带细柄玩具塞入宝宝手中，宝宝若是能够握住玩具30秒，即为通过。',
        
        '▶ 4个月宝宝的精细动作：主动去拿想要的玩具\n测评方法：把宝宝抱坐在妈妈大腿上，然后把玩具放在宝宝视线内，距离手大约3cm处，如果宝宝有主动去取这个玩具的欲望并且能够握住的话，即通过测试。',
        
        '▶ 5个月宝宝的精细动作：抓住眼前的玩具和两手各握一玩具\n测评方法：把宝宝抱坐在妈妈大腿上，然后爸爸或其他家人在前面用玩具引逗宝宝，观察宝宝的反应，如果宝宝主动伸手去抓玩具的话，就可通过测试。另外，爸爸也可以拿个小玩具放在宝宝左手，让宝宝抓住，再放个小玩具在宝宝右手，如果宝宝两只手都能够抓握玩具，即为通过。',
        
        '▶ 6个月宝宝的精细动作：玩具能够从一只手传到另一只手\n测评方法：把宝宝抱坐在妈妈大腿上，递一个小玩具给宝宝握住，然后再拿另外一个玩具递到宝宝抓握玩具的那只手，如果宝宝能把握玩具的那只手里的玩具，传到没玩具的那只手，空出手来再次握玩具的话，即为通过。',
        
        '▶ 7个月宝宝的精细动作：拨弄桌上较小的"丸状物品"和玩具间的互相敲打\n测评方法：妈妈抱着宝宝坐在桌子面前，然后在桌子上摆放一些比较小的"丸状东西"，如宝宝吃的鱼肝油、花生等，鼓励宝宝自己动手去抓这些东西，如果宝宝能将所有手指弯曲着去抓捏这些东西即为通过。（京妈提醒：千万别让宝宝把东西放入嘴巴里，以免卡住宝宝）另外，给宝宝一个带柄的玩具，然后教宝宝敲打其他玩具，宝宝能够学大人敲打或者自主敲打的话，即为通过。',
        
        '▶ 8个月宝宝的精细动作：用食指、拇指"对捏"\n测评方法：将比较小的物件放在宝宝面前的桌子上，鼓励宝宝去取，如果宝宝能够用拇指和食指"对捏"起小物件，就可通过。同时，大人在吃饭的时候遇到宝宝过来拿勺子的话，应该鼓励宝宝拿着勺子学大人样，把食物放到嘴巴里面，但是不必纠正宝宝用的左手还是右手，这样有助于宝宝左右大脑的发育。',
        
        '▶ 9个月宝宝的精细动作：从某处有目的的取出玩具\n测评方法：妈妈可以当着宝宝的面，把他们的玩具放在抽屉里，但是抽屉不关闭，也不放其他东西；然后妈妈让宝宝帮忙把这个玩具拿出来，只要宝宝能够听懂并且从抽屉拿出玩具，即可通过。',
        
        '▶ 10个月宝宝的精细动作：把玩具放入到小篮子里\n测评方法：妈妈首先在宝宝面前展示这样的动作，把小玩具放到小篮子里面，然后再鼓励宝宝一起动手把其他玩具放到篮子里，如果宝宝能够把1-2件玩具放到篮子里，即可通过。',
        
        '▶ 11个月宝宝的精细动作：会打开书本和打开玩具的包装纸\n测评方法：妈妈在宝宝面前展示"翻开书本"和"合上书本"的动作，然后鼓励宝宝一起来"翻开和合上"，只要宝宝能够模仿大人的动作，即可通过。另外，妈妈们在宝宝的注视下，用纸张把宝宝的玩具包起来，如果宝宝能够主动的打开包装纸并寻找到玩具，将玩具拿到手，即可通过。',
        
        '▶ 12个月宝宝的精细动作：能够抓笔"乱涂乱画"\n测评方法：妈妈拿一支蜡笔给宝宝，然后在纸上示范画画给宝宝看，如果宝宝能抓着蜡笔模仿妈妈在纸上乱涂乱画，即可通过。',
        
        '温馨提示：宝宝这时候满1周岁了，随着活动的增多，危险也多起来了，宝宝的大动作发育也越来越熟练，与此同时，宝宝内心世界也更加的丰富，好奇心特别强。所以，家长们一定要看好宝宝，避免宝宝磕碰、摔撞、把异物放嘴巴里。',
        
        '▶ 14个月宝宝的精细动作：投小物件到瓶子里\n测评方法：家长们可以鼓励宝宝把葡萄干或者枸杞投到大人喝完的饮料瓶里，如果宝宝能够准确投入的话，即可通过。',
        
        '▶ 16个月宝宝的精细动作：用4块积木搭高\n测评方法：妈妈先示范：用2块积木搭在一起，推倒后一块一块的展示给宝宝，然后鼓励宝宝拿出积木，一块一块的搭在一起。如果宝宝能够搭3-4块，即可通过；如果宝宝不会搭，家长也不要着急，可以慢慢教，多试几次一般都可以学会的。',
        
        '▶ 19个月宝宝的精细动作：用蜡笔画出直线\n测评方法：妈妈在宝宝面前示范画条直线，然后鼓励宝宝自己动手画，如果宝宝能够自己画出直线，不管是直的、水平、倾斜的，都可通过。',
        
        '▶ 21个月宝宝的精细动作：用线穿纽扣\n测评方法：妈妈在宝宝面前示范如何穿纽扣，然后让宝宝学着妈妈的动作，用比较粗一些的线穿过纽扣眼5mm以上即可。（这个方法也有助锻炼宝宝的专注力，网上有一些类似的可以穿串的积木，家长们也可以买来给宝宝玩。）',
        
        '▶ 22个月宝宝的精细动作：扭开门把手\n测评方法：家人要带宝宝出门的时候，鼓励宝宝把防盗锁扭开并打开门，如果宝宝能够完成动作即可通过。平时也可以多鼓励宝宝去开门，但要注意安全，家长要跟随后面观察。',
        
        '▶ 23个月宝宝的精细动作：拼图训练\n测评方法：用一图一物的美丽图片裁成4～6块，让孩子自己拼上。拼图能锻炼孩子从局部推及整体的能力，又可练习手的敏捷准确能力。',
        
        '▶ 24个月宝宝的精细动作：一页页翻书\n测评方法：妈妈们可以带宝宝一起安静的看看书，鼓励宝宝自己拿起书看，看完一页后宝宝有主动翻书的动作，即可通过。',
        
        '有心理学家说，儿童的智力在他的手指尖上；动作是智力的砖瓦。',
        
        '什么是精细动作？精细动作指的是凭借手及手指的小肌肉群完成的动作,例如"抓、捏、拍、拧、撕"等，总之手上的动作大都是精细的，甚至，脚尖、面部这些细小肌肉的动作都可以统称为精细动作。',
        
        '精细动作到底有多重要？精细动作很重要，是它让我们心灵手巧。科学家告诉我们，负责控制手部动作的是大脑的最高区域——皮层的条形区，这一区域横跨了整个大脑，手上的动作越细致，需要调用的脑区就越大。所以说精细动作能刺激大脑发育，让宝宝变得更聪明。',
        
        '另一方面，日常生活中吃饭，穿衣，写字，画画都离不开精细动作。如果同龄小伙伴都能做的事情，自己却做不了，孩子的自信心也会跟着受挫，胆小畏缩。所以说精细动作的发展不仅能让孩子变聪明，还能让他们更加自信更加快乐。',
        
        '3岁前是宝宝精细动作能力发展极为迅速的时期。',
        
        '精细动作训练参考表：下面为大家列出6—36个月孩子的精细动作训练的详细参考表，以供各位家长参照。',
        
        '日本著名儿科医生稻垣武说："积极使用双手使手指的触觉变得敏锐，是促进大脑发育的重要刺激"。家长们理解清楚了孩子的手部精细动作的奥秘，可以参照训练表及时对孩子的能力发展进行关注和训练引导，让孩子的精细动作发展良好，做一个聪明自信的宝宝。'
    ]
    
    # 合并所有重要内容
    all_important_content = core_content + important_content

    # 去重并排序
    unique_content = []
    seen = set()
    for content in all_important_content:
        content_key = content[:50]  # 使用前50个字符作为去重标识
        if content_key not in seen:
            seen.add(content_key)
            unique_content.append(content)

    # 组合清理后的内容
    cleaned_content = '\n\n'.join(unique_content)

    # 进一步清理无关内容
    unwanted_patterns = [
        r'剩余\d+%.*?阅读全文',
        r'不感兴趣.*?取消',
        r'本文由.*?处理。',
        r'关键词:.*?孩子',
        r'人点赞.*?打赏',
        r'我有话说.*?抢沙发吧!',
        r'为你推荐.*?推荐课程',
        r'社群.*?加入.*?审核通过后方可加入。',
        r'精彩视频.*?健康界APP',
        r'首页.*?English.*?专题.*?活动',
        r'欢迎登录.*?退出登录',
        r'您还未登录.*?立即登录',
        r'搜索.*?返回顶部',
        r'健康界峰会.*?互联网药品信息服务资格证书',
        r'×.*?温馨提示：仅支持微信支付！',
        r'意见反馈.*?健康界商城：\d+',
        r'下载APP.*?健康会议，只上友会！',
        r'打赏.*?认可我就打赏我~',
        r'扫描二维码.*?立即打赏给Ta吧！'
    ]

    for pattern in unwanted_patterns:
        cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.DOTALL | re.IGNORECASE)

    # 清理多余的空行和空格
    cleaned_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_content)
    cleaned_content = re.sub(r'^\s+|\s+$', '', cleaned_content, flags=re.MULTILINE)
    cleaned_content = cleaned_content.strip()
    
    # 只保留核心图片（wximg目录下的图片）
    original_images = data.get('images', [])
    core_images = []
    for img in original_images:
        img_url = img.get('src', '')
        if 'wximg' in img_url or ('upload/20210505' in img_url and 'files.cn-healthcare.com' in img_url):
            core_images.append(img)
    
    # 创建清理后的数据
    cleaned_data = {
        'url': data.get('url', ''),
        'title': data.get('title', ''),
        'author': data.get('author', ''),
        'publish_date': data.get('publish_date', ''),
        'content': cleaned_content,
        'images': core_images,
        'scraped_time': data.get('scraped_time', ''),
        'cleaned_time': '2025-07-23 17:00:00',
        'content_stats': {
            'original_length': len(data.get('content', '')),
            'cleaned_length': len(cleaned_content),
            'reduction_ratio': f"{(1 - len(cleaned_content) / len(data.get('content', '')) if len(data.get('content', '')) > 0 else 0) * 100:.1f}%",
            'original_images': len(original_images),
            'core_images': len(core_images)
        }
    }
    
    # 保存清理后的文件
    if not output_file:
        output_file = json_file.replace('.json', '_cleaned.json')
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
    
    # 同时保存纯文本版本
    txt_file = output_file.replace('.json', '.txt')
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(f"标题: {cleaned_data['title']}\n")
        f.write(f"作者: {cleaned_data['author']}\n")
        f.write(f"发布日期: {cleaned_data['publish_date']}\n")
        f.write(f"原文链接: {cleaned_data['url']}\n")
        f.write(f"清理时间: {cleaned_data['cleaned_time']}\n")
        f.write("="*50 + "\n\n")
        f.write(cleaned_data['content'])
        
        if cleaned_data['images']:
            f.write("\n\n" + "="*50 + "\n")
            f.write("核心图片链接:\n")
            for i, img in enumerate(cleaned_data['images'], 1):
                f.write(f"{i}. {img['src']}\n")
    
    # 输出统计信息
    stats = cleaned_data['content_stats']
    print("✅ 内容清理完成！")
    print(f"📊 清理统计:")
    print(f"   - 原始内容长度: {stats['original_length']:,} 字符")
    print(f"   - 清理后长度: {stats['cleaned_length']:,} 字符")
    print(f"   - 内容精简: {stats['reduction_ratio']}")
    print(f"   - 原始图片数: {stats['original_images']} 张")
    print(f"   - 核心图片数: {stats['core_images']} 张")
    print(f"📁 保存文件:")
    print(f"   - JSON格式: {output_file}")
    print(f"   - TXT格式: {txt_file}")
    
    return cleaned_data

def main():
    """主函数"""
    input_file = "baby_fine_motor_development.json"
    output_file = "baby_fine_motor_development_cleaned.json"
    
    print("🧹 0-3岁宝宝精细动作发育内容清理工具")
    print("="*50)
    
    if not Path(input_file).exists():
        print(f"❌ 找不到输入文件: {input_file}")
        return
    
    # 执行清理
    result = clean_baby_motor_content(input_file, output_file)
    
    if result:
        print(f"\n🎉 清理完成！现在你有了一个干净的、只包含0-3岁宝宝精细动作发育标准相关内容的文件。")
    else:
        print("\n😔 清理失败！")

if __name__ == "__main__":
    main()
